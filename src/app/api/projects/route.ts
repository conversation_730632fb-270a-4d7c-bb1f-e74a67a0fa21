import { NextRequest, NextResponse } from "next/server";
import { findProjects } from "@/models/project";
import { findUserByUuid } from "@/models/user";

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "9");
    const search = searchParams.get("search") || undefined;
    const tag = searchParams.get("tag") || undefined;
    const sort = searchParams.get("sort") as "latest" | "popular" | "prompts" | "likes" || "latest";

    // Get projects from database
    const result = await findProjects(
      { search, tag },
      { page, limit, sort }
    );

    // Get user info for each project
    const projectsWithAuthors = await Promise.all(
      result.data.map(async (project) => {
        const user = await findUserByUuid(project.user_uuid);
        return {
          id: project.id,
          uuid: project.uuid,
          title: project.title,
          description: project.description,
          thumbnailUrl: project.thumbnail_url,
          tags: project.tags ? JSON.parse(project.tags) : [],
          promptsCount: project.prompts_count,
          devTime: project.dev_time,
          likesCount: project.likes_count,
          viewsCount: project.views_count,
          createdAt: project.created_at.toISOString(),
          author: {
            name: user?.nickname || user?.email || "Anonymous",
            avatar: user?.avatar_url || undefined,
          },
        };
      })
    );

    return NextResponse.json({
      success: true,
      data: projectsWithAuthors,
      pagination: {
        page: result.page,
        limit: result.limit,
        total: result.total,
        totalPages: result.totalPages,
      },
    });
  } catch (error) {
    console.error("Failed to fetch projects:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch projects" },
      { status: 500 }
    );
  }
}