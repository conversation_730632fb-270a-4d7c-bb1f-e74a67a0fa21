"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import Icon from "@/components/icon";
import Image from "next/image";
import Link from "next/link";
import { useState, useEffect } from "react";
import { useInView } from "@/hooks/use-intersection-observer";
import { useTranslations } from "next-intl";

interface Project {
  id: number;
  uuid: string;
  title: string;
  description: string;
  thumbnailUrl?: string;
  tags: string[];
  promptsCount: number;
  devTime: string;
  likesCount: number;
  viewsCount: number;
  createdAt: string;
  author: {
    name: string;
    avatar?: string;
  };
}

interface ProjectsListProps {
  search?: string;
  tag?: string;
  sort?: string;
  page: number;
}

export default function ProjectsList({ search, tag, sort, page }: ProjectsListProps) {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(page);
  const t = useTranslations("projects");
  
  const { ref, inView } = useInView({
    threshold: 0,
    rootMargin: "100px",
  });

  // Fetch real data from API
  useEffect(() => {
    const fetchProjects = async () => {
      setLoading(true);
      
      try {
        const params = new URLSearchParams({
          page: currentPage.toString(),
          limit: '9',
          ...(search && { search }),
          ...(tag && { tag }),
          ...(sort && { sort }),
        });
        
        const response = await fetch(`/api/projects?${params}`);
        const result = await response.json();
        
        if (result.success) {
          if (currentPage === 1) {
            setProjects(result.data);
          } else {
            setProjects(prev => [...prev, ...result.data]);
          }
          
          // Check if there are more pages
          setHasMore(currentPage < result.pagination.totalPages);
        } else {
          console.error('Failed to fetch projects:', result.error);
          setHasMore(false);
        }
      } catch (error) {
        console.error('Error fetching projects:', error);
        setHasMore(false);
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, [currentPage, search, tag, sort]);

  // Reset when filters change
  useEffect(() => {
    setProjects([]);
    setCurrentPage(1);
    setHasMore(true);
  }, [search, tag, sort]);

  useEffect(() => {
    if (inView && hasMore && !loading) {
      setCurrentPage(prev => prev + 1);
    }
  }, [inView, hasMore, loading]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (days === 0) return t("today");
    if (days === 1) return t("yesterday");
    if (days < 7) return t("daysAgo", { days });
    if (days < 30) return t("weeksAgo", { weeks: Math.floor(days / 7) });
    return t("monthsAgo", { months: Math.floor(days / 30) });
  };

  return (
    <div className="mt-12">
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {projects.map((project) => (
          <Link key={project.id} href={`/projects/${project.uuid}`}>
            <Card className="group h-full cursor-pointer overflow-hidden transition-all hover:shadow-xl hover:-translate-y-1">
              <div className="relative aspect-video overflow-hidden bg-muted">
                {project.thumbnailUrl ? (
                  <Image
                    src={project.thumbnailUrl}
                    alt={project.title}
                    fill
                    className="object-cover transition-transform duration-300 group-hover:scale-110"
                  />
                ) : (
                  <div className="flex h-full items-center justify-center">
                    <Icon name="RiCodeBoxLine" className="h-12 w-12 text-muted-foreground" />
                  </div>
                )}
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 transition-opacity group-hover:opacity-100" />
                <Badge className="absolute right-2 top-2 bg-black/50 backdrop-blur-sm">
                  <Icon name="RiEyeLine" className="mr-1 h-3 w-3" />
                  {t("views", { count: project.viewsCount })}
                </Badge>
              </div>
              
              <CardHeader className="space-y-2">
                <h3 className="line-clamp-1 text-xl font-semibold group-hover:text-primary transition-colors">
                  {project.title}
                </h3>
                <p className="line-clamp-2 text-sm text-muted-foreground">
                  {project.description}
                </p>
              </CardHeader>
              
              <CardContent className="space-y-4">
                <div className="flex flex-wrap gap-2">
                  {project.tags.map((tag, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
                
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Icon name="RiChat3Line" className="h-4 w-4" />
                    <span>{t("prompts", { count: project.promptsCount })}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Icon name="RiTimeLine" className="h-4 w-4" />
                    <span>{t("devTime", { time: project.devTime })}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Icon name="RiHeartLine" className="h-4 w-4" />
                    <span>{t("likes", { count: project.likesCount })}</span>
                  </div>
                </div>
              </CardContent>
              
              <CardFooter className="border-t pt-4">
                <div className="flex items-center justify-between w-full">
                  <div className="flex items-center gap-2">
                    <div className="relative h-6 w-6 overflow-hidden rounded-full bg-muted">
                      {project.author.avatar ? (
                        <Image
                          src={project.author.avatar}
                          alt={project.author.name}
                          fill
                          className="object-cover"
                        />
                      ) : (
                        <Icon name="RiUserLine" className="h-full w-full p-1" />
                      )}
                    </div>
                    <span className="text-sm font-medium">{project.author.name}</span>
                  </div>
                  <span className="text-xs text-muted-foreground">
                    {formatDate(project.createdAt)}
                  </span>
                </div>
              </CardFooter>
            </Card>
          </Link>
        ))}
      </div>
      
      {hasMore && (
        <div ref={ref} className="mt-12 flex justify-center">
          {loading && (
            <div className="flex items-center gap-2">
              <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
              <span className="text-sm text-muted-foreground">{t("loadingMore")}</span>
            </div>
          )}
        </div>
      )}
      
      {!hasMore && projects.length > 0 && (
        <div className="mt-12 text-center">
          <p className="text-sm text-muted-foreground">
            {t("endOfList", { count: projects.length })}
          </p>
        </div>
      )}
      
      {projects.length === 0 && !loading && (
        <div className="mt-12 text-center">
          <Icon name="RiEmotionSadLine" className="mx-auto h-12 w-12 text-muted-foreground" />
          <p className="mt-4 text-lg font-medium">{t("noResults")}</p>
          <p className="mt-2 text-sm text-muted-foreground">
            {t("noResultsDescription")}
          </p>
        </div>
      )}
    </div>
  );
}