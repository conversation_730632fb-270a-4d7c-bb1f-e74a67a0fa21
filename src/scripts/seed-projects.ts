import dotenv from "dotenv";
import { v4 as uuidv4 } from "uuid";

// Load environment variables FIRST before importing db
dotenv.config({ path: '.env' });

// Debug: Check if DATABASE_URL is loaded
console.log("DATABASE_URL loaded:", !!process.env.DATABASE_URL);

import { db } from "@/db";
import { projects, conversations, users } from "@/db/schema";

// Sample project data
const sampleProjects = [
  {
    title: "AI-Powered Todo App",
    description: "A smart todo application built with Next.js and Claude API. Features include natural language task parsing, priority detection, and intelligent categorization.",
    tags: ["React", "Next.js", "TypeScript", "Claude API", "Tailwind CSS"],
    promptsCount: 45,
    devTime: "3 hours",
  },
  {
    title: "Real-time Chat Application",
    description: "WebSocket-based chat application with rooms, typing indicators, and message history. Built using Claude Code through conversational development.",
    tags: ["Socket.io", "Node.js", "React", "MongoDB"],
    promptsCount: 62,
    devTime: "5 hours",
  },
  {
    title: "E-commerce Dashboard",
    description: "Modern admin dashboard for e-commerce platforms with sales analytics, inventory management, and customer insights.",
    tags: ["React", "Chart.js", "Material-UI", "PostgreSQL"],
    promptsCount: 78,
    devTime: "6 hours",
  },
  {
    title: "Weather Forecast App",
    description: "Beautiful weather application with 7-day forecasts, interactive maps, and weather alerts. Integrates with multiple weather APIs.",
    tags: ["Vue.js", "OpenWeatherMap API", "Leaflet", "CSS Grid"],
    promptsCount: 34,
    devTime: "2.5 hours",
  },
  {
    title: "Markdown Blog Engine",
    description: "Static site generator for blogs with markdown support, syntax highlighting, and SEO optimization. Built entirely through Claude conversations.",
    tags: ["Next.js", "MDX", "Prism.js", "SEO"],
    promptsCount: 56,
    devTime: "4 hours",
  },
  {
    title: "Task Automation Tool",
    description: "Node.js CLI tool for automating repetitive tasks with cron jobs, file watching, and webhook integrations.",
    tags: ["Node.js", "Commander.js", "Cron", "TypeScript"],
    promptsCount: 41,
    devTime: "3.5 hours",
  },
  {
    title: "Portfolio Website Builder",
    description: "Drag-and-drop website builder specifically designed for developer portfolios. Features theme customization and GitHub integration.",
    tags: ["React", "React DnD", "Styled Components", "GitHub API"],
    promptsCount: 89,
    devTime: "7 hours",
  },
  {
    title: "API Monitoring Service",
    description: "Real-time API monitoring with uptime tracking, response time analytics, and alert notifications via email and Slack.",
    tags: ["Express", "Redis", "Chart.js", "Nodemailer"],
    promptsCount: 67,
    devTime: "5.5 hours",
  },
  {
    title: "Code Snippet Manager",
    description: "Desktop application for managing and organizing code snippets with syntax highlighting, tags, and search functionality.",
    tags: ["Electron", "React", "SQLite", "Monaco Editor"],
    promptsCount: 72,
    devTime: "6 hours",
  },
  {
    title: "Social Media Scheduler",
    description: "Schedule and manage posts across multiple social media platforms with analytics and optimal posting time suggestions.",
    tags: ["Next.js", "Prisma", "Twitter API", "Facebook API"],
    promptsCount: 93,
    devTime: "8 hours",
  },
  {
    title: "Image Gallery Component",
    description: "Reusable React component for image galleries with lazy loading, lightbox, and infinite scroll. Fully accessible and responsive.",
    tags: ["React", "Intersection Observer", "CSS Grid", "TypeScript"],
    promptsCount: 28,
    devTime: "2 hours",
  },
  {
    title: "Password Manager PWA",
    description: "Progressive web app for managing passwords with encryption, biometric authentication, and cloud sync capabilities.",
    tags: ["React", "IndexedDB", "Web Crypto API", "PWA"],
    promptsCount: 85,
    devTime: "7.5 hours",
  },
];

// Sample conversation templates
const conversationTemplates = [
  {
    user: "I want to create a {projectType}. Can you help me get started?",
    assistant: "I'll help you create a {projectType}. Let's start by setting up the project structure and dependencies. First, let me create the necessary files and configurations..."
  },
  {
    user: "Add a feature to {feature}",
    assistant: "I'll implement the {feature} feature for you. Here's what I'll do:\n\n1. Create the necessary components\n2. Set up the logic\n3. Add appropriate styling\n\nLet me start by creating the main component..."
  },
  {
    user: "There's a bug when {action}. Can you fix it?",
    assistant: "I see the issue. When {action}, the application isn't handling the state correctly. Let me fix this by updating the error handling and state management..."
  },
  {
    user: "Can you improve the performance of {component}?",
    assistant: "I'll optimize the {component} for better performance. Here are the improvements I'll make:\n\n1. Implement memoization\n2. Add lazy loading\n3. Optimize re-renders\n\nLet me update the code..."
  },
  {
    user: "Add tests for the {module}",
    assistant: "I'll add comprehensive tests for the {module}. I'll create unit tests for the main functions and integration tests for the API endpoints..."
  }
];

async function seedDatabase() {
  try {
    console.log("🌱 Starting database seeding...");

    // First, check if we have any users
    const existingUsers = await db.select().from(users).limit(1);
    
    let testUserUuid: string;
    
    if (existingUsers.length === 0) {
      // Create a test user
      console.log("Creating test user...");
      const [testUser] = await db.insert(users).values({
        uuid: uuidv4(),
        email: "<EMAIL>",
        nickname: "Demo User",
        avatar_url: "/imgs/users/1.png",
        created_at: new Date(),
        updated_at: new Date(),
      }).returning();
      testUserUuid = testUser.uuid;
    } else {
      testUserUuid = existingUsers[0].uuid;
      console.log("Using existing user for seeding...");
    }

    // Create projects
    console.log("Creating projects...");
    for (let i = 0; i < sampleProjects.length; i++) {
      const projectData = sampleProjects[i];
      const projectUuid = uuidv4();
      
      // Random metrics
      const likesCount = Math.floor(Math.random() * 500) + 50;
      const viewsCount = Math.floor(Math.random() * 2000) + 200;
      const createdDaysAgo = Math.floor(Math.random() * 60);
      const createdAt = new Date();
      createdAt.setDate(createdAt.getDate() - createdDaysAgo);

      // Insert project
      const [project] = await db.insert(projects).values({
        uuid: projectUuid,
        user_uuid: testUserUuid,
        title: projectData.title,
        description: projectData.description,
        thumbnail_url: `/imgs/showcases/${(i % 9) + 1}.png`,
        tags: JSON.stringify(projectData.tags),
        prompts_count: projectData.promptsCount,
        dev_time: projectData.devTime,
        likes_count: likesCount,
        views_count: viewsCount,
        status: "published",
        created_at: createdAt,
        updated_at: createdAt,
      }).returning();

      console.log(`✅ Created project: ${project.title}`);

      // Create conversations for this project
      const conversationCount = Math.min(projectData.promptsCount, 10); // Limit to 10 conversations per project
      
      for (let j = 0; j < conversationCount; j++) {
        const template = conversationTemplates[j % conversationTemplates.length];
        const conversationUuid = uuidv4();
        
        // User message
        await db.insert(conversations).values({
          uuid: conversationUuid,
          project_uuid: projectUuid,
          sequence: j * 2 + 1,
          role: "user",
          content: template.user
            .replace("{projectType}", projectData.title.toLowerCase())
            .replace("{feature}", "user authentication")
            .replace("{action}", "users click the submit button")
            .replace("{component}", "the main list view")
            .replace("{module}", "authentication module"),
          created_at: new Date(createdAt.getTime() + j * 1000 * 60 * 5), // 5 minutes apart
          updated_at: new Date(createdAt.getTime() + j * 1000 * 60 * 5),
        });

        // Assistant response
        await db.insert(conversations).values({
          uuid: uuidv4(),
          project_uuid: projectUuid,
          sequence: j * 2 + 2,
          role: "assistant",
          content: template.assistant
            .replace("{projectType}", projectData.title.toLowerCase())
            .replace("{feature}", "user authentication")
            .replace("{action}", "users click the submit button")
            .replace("{component}", "the main list view")
            .replace("{module}", "authentication module"),
          code: j % 3 === 0 ? `// Example code for ${projectData.title}\nfunction example() {\n  console.log("This is generated by Claude");\n  return true;\n}` : null,
          created_at: new Date(createdAt.getTime() + j * 1000 * 60 * 5 + 30000), // 30 seconds after user
          updated_at: new Date(createdAt.getTime() + j * 1000 * 60 * 5 + 30000),
        });
      }
      
      console.log(`  📝 Added ${conversationCount * 2} conversations`);
    }

    console.log("\n✨ Database seeding completed successfully!");
    console.log(`📊 Created ${sampleProjects.length} projects with conversations`);

  } catch (error) {
    console.error("❌ Error seeding database:", error);
    process.exit(1);
  } finally {
    process.exit(0);
  }
}

// Run the seed function
seedDatabase();