import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { JsonlParser } from "@/services/jsonl-parser";
import { createProject } from "@/models/project";
import { db } from "@/db";
import { conversations } from "@/db/schema";
import { v4 as uuidv4 } from "uuid";

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get form data
    const formData = await request.formData();
    const file = formData.get("file") as File;
    
    if (!file) {
      return NextResponse.json(
        { success: false, error: "No file provided" },
        { status: 400 }
      );
    }

    // Parse the JSONL file
    const fileContent = await file.text();
    
    try {
      const projectData = JsonlParser.parseJsonl(fileContent);
      const conversationData = projectData.conversations;
      const conversationUuids = conversationData.map(() => uuidv4());

      // Save project to database
      const project = await createProject({
        userUuid: session.user.id,
        title: projectData.title || "Untitled Project",
        description: projectData.description || "",
        tags: projectData.tags,
        promptsCount: projectData.promptsCount,
        devTime: projectData.devTime || "",
        status: "published",
      });

      // Save conversations
      const database = db;
      for (let i = 0; i < conversationData.length; i++) {
        const conv = conversationData[i];
        await database.insert(conversations).values({
          uuid: conversationUuids[i],
          projectUuid: project.uuid,
          sequence: i + 1,
          role: conv.role,
          content: conv.content,
          code: conv.code || null,
          annotations: JSON.stringify(conv.annotations || []),
          createdAt: new Date(),
        });
      }

      return NextResponse.json({
        success: true,
        data: {
          projectId: project.uuid,
          conversationCount: conversationData.length,
        },
      });
    } catch (parseError) {
      console.error("Error parsing JSONL file:", parseError);
      return NextResponse.json(
        { success: false, error: "Failed to parse JSONL file" },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error("Error submitting project:", error);
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}