import { notFound } from "next/navigation";
import { auth } from "@/auth";
import { findProjectByUuid } from "@/models/project";
import { db } from "@/db";
import { conversations } from "@/db/schema";
import { eq } from "drizzle-orm";
import { setRequestLocale, getTranslations } from "next-intl/server";
import ProjectDetail from "@/components/blocks/project-detail";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string; uuid: string }>;
}) {
  const { locale, uuid } = await params;
  const project = await findProjectByUuid(uuid);
  const t = await getTranslations({ locale, namespace: "projectDetailPage" });
  
  if (!project) {
    return {
      title: t("notFound"),
    };
  }
  
  return {
    title: `${project.title} - Claude Code Show`,
    description: project.description || t("defaultDescription"),
  };
}

export default async function ProjectDetailPage({
  params,
}: {
  params: Promise<{ locale: string; uuid: string }>;
}) {
  const { locale, uuid } = await params;
  setRequestLocale(locale);
  
  const project = await findProjectByUuid(uuid);
  
  if (!project) {
    notFound();
  }
  
  // Get session to check permissions
  const session = await auth();
  const isOwner = session?.user?.id === project.user_uuid;
  const isPro = session?.user?.subscription_status === "active";
  const canViewAllConversations = isOwner || isPro;
  
  // Get conversations
  const database = db;
  const projectConversations = await database
    .select()
    .from(conversations)
    .where(eq(conversations.project_uuid, uuid))
    .orderBy(conversations.sequence);
  
  // Format data for component (convert snake_case to camelCase)
  const projectData = {
    uuid: project.uuid,
    title: project.title,
    description: project.description,
    tags: project.tags ? JSON.parse(project.tags) : [],
    promptsCount: project.prompts_count,
    devTime: project.dev_time,
    likesCount: project.likes_count,
    viewsCount: project.views_count,
    createdAt: project.created_at,
    conversations: canViewAllConversations 
      ? projectConversations
      : projectConversations.slice(0, 3), // Only show first 3 for non-pro users
    isLimited: !canViewAllConversations && projectConversations.length > 3,
  };
  
  return <ProjectDetail project={projectData} isOwner={isOwner} />;
}