import dotenv from "dotenv";
import { v4 as uuidv4 } from "uuid";

// Load environment variables FIRST before importing db
dotenv.config({ path: '.env' });

import { db } from "@/db";
import { projects, conversations, users } from "@/db/schema";

// Sample project data
const sampleProjects = [
  {
    title: "AI-Powered Todo App",
    description: "A smart todo application built with Next.js and Claude API. Features include natural language task parsing, priority detection, and intelligent categorization.",
    tags: ["React", "Next.js", "TypeScript", "Claude API", "Tailwind CSS"],
    promptsCount: 15,
    devTime: "3 hours",
  },
  {
    title: "Real-time Chat Application",
    description: "WebSocket-based chat application with rooms, typing indicators, and message history. Built using Claude Code through conversational development.",
    tags: ["Socket.io", "Node.js", "React", "MongoDB"],
    promptsCount: 22,
    devTime: "5 hours",
  },
  {
    title: "E-commerce Dashboard",
    description: "Modern admin dashboard for e-commerce platforms with sales analytics, inventory management, and customer insights.",
    tags: ["React", "Chart.js", "Material-UI", "PostgreSQL"],
    promptsCount: 28,
    devTime: "6 hours",
  },
  {
    title: "Weather Forecast App",
    description: "Beautiful weather application with 7-day forecasts, interactive maps, and weather alerts. Integrates with multiple weather APIs.",
    tags: ["Vue.js", "OpenWeatherMap API", "Leaflet", "CSS Grid"],
    promptsCount: 14,
    devTime: "2.5 hours",
  },
  {
    title: "Markdown Blog Engine",
    description: "Static site generator for blogs with markdown support, syntax highlighting, and SEO optimization. Built entirely through Claude conversations.",
    tags: ["Next.js", "MDX", "Prism.js", "SEO"],
    promptsCount: 26,
    devTime: "4 hours",
  },
  {
    title: "Task Automation Tool",
    description: "Node.js CLI tool for automating repetitive tasks with cron jobs, file watching, and webhook integrations.",
    tags: ["Node.js", "Commander.js", "Cron", "TypeScript"],
    promptsCount: 21,
    devTime: "3.5 hours",
  },
];

// Sample conversation templates
const conversationTemplates = [
  {
    user: "I want to create a new project. Can you help me get started?",
    assistant: "I'll help you create this project. Let's start by setting up the project structure and dependencies."
  },
  {
    user: "Add user authentication to the application",
    assistant: "I'll implement user authentication for you. Here's what I'll do:\n\n1. Set up authentication routes\n2. Create login/signup forms\n3. Implement JWT tokens\n\nLet me start by creating the authentication components..."
  },
  {
    user: "There's a bug when users click submit. Can you fix it?",
    assistant: "I see the issue. When users click submit, the form isn't properly validated. Let me fix this by adding proper error handling..."
  },
];

async function seedDatabase() {
  try {
    console.log("🌱 Starting fast database seeding...");

    // Check if we have any users
    const existingUsers = await db.select().from(users).limit(1);
    
    let testUserUuid: string;
    
    if (existingUsers.length === 0) {
      // Create a test user
      console.log("Creating test user...");
      const [testUser] = await db.insert(users).values({
        uuid: uuidv4(),
        email: "<EMAIL>",
        nickname: "Demo User",
        avatar_url: "/imgs/users/1.png",
        created_at: new Date(),
        updated_at: new Date(),
      }).returning();
      testUserUuid = testUser.uuid;
    } else {
      testUserUuid = existingUsers[0].uuid;
      console.log("Using existing user for seeding...");
    }

    // Prepare all projects data
    const projectsData = sampleProjects.map((projectData, i) => {
      const projectUuid = uuidv4();
      const likesCount = Math.floor(Math.random() * 500) + 50;
      const viewsCount = Math.floor(Math.random() * 2000) + 200;
      const createdDaysAgo = Math.floor(Math.random() * 60);
      const createdAt = new Date();
      createdAt.setDate(createdAt.getDate() - createdDaysAgo);

      return {
        uuid: projectUuid,
        user_uuid: testUserUuid,
        title: projectData.title,
        description: projectData.description,
        thumbnail_url: `/imgs/showcases/${(i % 9) + 1}.png`,
        tags: JSON.stringify(projectData.tags),
        prompts_count: projectData.promptsCount,
        dev_time: projectData.devTime,
        likes_count: likesCount,
        views_count: viewsCount,
        status: "published" as const,
        created_at: createdAt,
        updated_at: createdAt,
      };
    });

    // Insert all projects at once
    console.log("Inserting projects...");
    const insertedProjects = await db.insert(projects).values(projectsData).returning();
    console.log(`✅ Created ${insertedProjects.length} projects`);

    // Prepare all conversations data
    const conversationsData: any[] = [];
    
    insertedProjects.forEach((project, projectIndex) => {
      const conversationCount = Math.min(projectsData[projectIndex].prompts_count, 5); // Limit to 5 conversations per project
      
      for (let j = 0; j < conversationCount; j++) {
        const template = conversationTemplates[j % conversationTemplates.length];
        const baseTime = project.created_at.getTime();
        
        // User message
        conversationsData.push({
          uuid: uuidv4(),
          project_uuid: project.uuid,
          sequence: j * 2 + 1,
          role: "user",
          content: template.user,
          created_at: new Date(baseTime + j * 1000 * 60 * 5), // 5 minutes apart
          updated_at: new Date(baseTime + j * 1000 * 60 * 5),
        });

        // Assistant response
        conversationsData.push({
          uuid: uuidv4(),
          project_uuid: project.uuid,
          sequence: j * 2 + 2,
          role: "assistant",
          content: template.assistant,
          code: j === 0 ? `// Example code\nfunction example() {\n  console.log("Generated by Claude");\n}` : null,
          created_at: new Date(baseTime + j * 1000 * 60 * 5 + 30000), // 30 seconds after user
          updated_at: new Date(baseTime + j * 1000 * 60 * 5 + 30000),
        });
      }
    });

    // Insert all conversations at once
    console.log("Inserting conversations...");
    await db.insert(conversations).values(conversationsData);
    console.log(`📝 Added ${conversationsData.length} conversations`);

    console.log("\n✨ Database seeding completed successfully!");

  } catch (error) {
    console.error("❌ Error seeding database:", error);
    process.exit(1);
  } finally {
    process.exit(0);
  }
}

// Run the seed function
seedDatabase();