import { db } from "@/db";
import { sql } from "drizzle-orm";
import dotenv from "dotenv";

// Load environment variables
dotenv.config();

async function initDatabase() {
  try {
    console.log("Initializing database tables...");
    
    const database = db;
    
    // Create projects table if not exists
    await database.execute(sql`
      CREATE TABLE IF NOT EXISTS "projects" (
        "id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
        "uuid" varchar(255) NOT NULL,
        "user_uuid" varchar(255) NOT NULL,
        "title" varchar(255) NOT NULL,
        "description" text,
        "thumbnail_url" varchar(500),
        "tags" text,
        "prompts_count" integer DEFAULT 0 NOT NULL,
        "dev_time" varchar(50),
        "likes_count" integer DEFAULT 0 NOT NULL,
        "views_count" integer DEFAULT 0 NOT NULL,
        "status" varchar(50) DEFAULT 'draft' NOT NULL,
        "created_at" timestamp with time zone DEFAULT now() NOT NULL,
        "updated_at" timestamp with time zone DEFAULT now() NOT NULL,
        "deleted_at" timestamp with time zone,
        CONSTRAINT "projects_uuid_unique" UNIQUE("uuid")
      )
    `);
    
    console.log("✅ Projects table ready");
    
    // Create conversations table if not exists
    await database.execute(sql`
      CREATE TABLE IF NOT EXISTS "conversations" (
        "id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
        "uuid" varchar(255) NOT NULL,
        "project_uuid" varchar(255) NOT NULL,
        "sequence" integer NOT NULL,
        "role" varchar(50) NOT NULL,
        "content" text NOT NULL,
        "code" text,
        "annotations" text,
        "created_at" timestamp with time zone DEFAULT now() NOT NULL,
        CONSTRAINT "conversations_uuid_unique" UNIQUE("uuid")
      )
    `);
    
    console.log("✅ Conversations table ready");
    
    // Create indexes
    await database.execute(sql`CREATE INDEX IF NOT EXISTS "idx_projects_user_uuid" ON "projects" ("user_uuid")`);
    await database.execute(sql`CREATE INDEX IF NOT EXISTS "idx_projects_status" ON "projects" ("status")`);
    await database.execute(sql`CREATE INDEX IF NOT EXISTS "idx_conversations_project_uuid" ON "conversations" ("project_uuid")`);
    await database.execute(sql`CREATE INDEX IF NOT EXISTS "idx_conversations_sequence" ON "conversations" ("project_uuid", "sequence")`);
    
    console.log("✅ Indexes created");
    console.log("Database initialization complete!");
    
  } catch (error) {
    console.error("Error initializing database:", error);
    process.exit(1);
  }
  
  process.exit(0);
}

initDatabase();